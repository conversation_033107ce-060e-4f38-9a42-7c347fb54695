#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
依赖安装脚本
自动安装天安门预约自动填写工具所需的Python库
"""

import subprocess
import sys
import os

def install_package(package_name):
    """安装Python包"""
    try:
        print(f"正在安装 {package_name}...")
        result = subprocess.run([
            sys.executable, "-m", "pip", "install", package_name
        ], capture_output=True, text=True, check=True)
        print(f"✅ {package_name} 安装成功")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {package_name} 安装失败: {e}")
        print(f"错误输出: {e.stderr}")
        return False

def check_package(package_name):
    """检查包是否已安装"""
    try:
        __import__(package_name)
        print(f"✅ {package_name} 已安装")
        return True
    except ImportError:
        print(f"⚠️  {package_name} 未安装")
        return False

def main():
    """主函数"""
    print("🔧 天安门预约自动填写工具 - 依赖安装器")
    print("=" * 50)
    
    # 需要安装的包列表
    packages = [
        ("pywinauto", "Windows应用自动化库"),
        ("selenium", "浏览器自动化库")
    ]
    
    print("检查已安装的包...")
    all_installed = True
    
    for package, description in packages:
        if not check_package(package):
            all_installed = False
    
    if all_installed:
        print("\n🎉 所有依赖包都已安装！")
        return
    
    print(f"\n开始安装缺失的依赖包...")
    
    success_count = 0
    for package, description in packages:
        if not check_package(package):
            print(f"\n安装 {package} ({description})...")
            if install_package(package):
                success_count += 1
    
    print(f"\n📊 安装结果:")
    print(f"成功安装: {success_count} 个包")
    
    if success_count == len([p for p, _ in packages if not check_package(p[0])]):
        print("🎉 所有依赖包安装完成！")
        print("\n现在可以运行以下命令启动工具:")
        print("python run.py")
    else:
        print("⚠️  部分包安装失败，请手动安装:")
        for package, description in packages:
            if not check_package(package):
                print(f"pip install {package}")

if __name__ == "__main__":
    main()
