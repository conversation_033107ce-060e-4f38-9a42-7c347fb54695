#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
天安门广场预约自动填写脚本
支持自动检测窗口并填写姓名、身份证等信息
"""

from pywinauto import Application
from pywinauto.findwindows import find_windows
import time
import json
import os
import re
from typing import Dict, List, Optional

class TianamenAutoFill:
    def __init__(self, config_file: str = "user_config.json"):
        """
        初始化自动填写工具
        
        Args:
            config_file: 用户配置文件路径
        """
        self.config_file = config_file
        self.user_data = self.load_user_config()
        self.app = None
        self.window = None
        
    def load_user_config(self) -> Dict:
        """加载用户配置信息"""
        if os.path.exists(self.config_file):
            try:
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except Exception as e:
                print(f"读取配置文件失败: {e}")
                return {}
        else:
            # 创建默认配置文件
            default_config = {
                "name": "",
                "id_card": "",
                "phone": "",
                "email": "",
                "note": "请在此文件中填写您的个人信息，脚本将自动读取"
            }
            self.save_user_config(default_config)
            print(f"已创建配置文件: {self.config_file}")
            print("请先填写您的个人信息后再运行脚本")
            return default_config
    
    def save_user_config(self, config: Dict):
        """保存用户配置信息"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"保存配置文件失败: {e}")
    
    def find_target_window(self) -> Optional[int]:
        """
        查找天安门预约相关窗口
        
        Returns:
            窗口句柄，如果未找到返回None
        """
        # 可能的窗口标题关键词
        keywords = [
            "天安门",
            "预约",
            "Tiananmen",
            "reservation",
            "微信",
            "WeChat",
            "小程序"
        ]
        
        print("正在搜索天安门预约相关窗口...")
        
        for keyword in keywords:
            try:
                windows = find_windows(title_re=f".*{keyword}.*", visible_only=True)
                if windows:
                    print(f"找到包含'{keyword}'的窗口: {len(windows)}个")
                    for hwnd in windows:
                        app = Application(backend="uia").connect(handle=hwnd)
                        window = app.window(handle=hwnd)
                        title = window.window_text()
                        print(f"  - 窗口标题: {title}")
                        
                        # 如果标题包含天安门或预约，优先选择
                        if any(kw in title for kw in ["天安门", "预约"]):
                            print(f"✅ 选择目标窗口: {title}")
                            self.app = app
                            self.window = window
                            return hwnd
                    
                    # 如果没有找到明确的天安门预约窗口，返回第一个匹配的
                    if not self.window:
                        hwnd = windows[0]
                        app = Application(backend="uia").connect(handle=hwnd)
                        window = app.window(handle=hwnd)
                        print(f"⚠️  使用第一个匹配窗口: {window.window_text()}")
                        self.app = app
                        self.window = window
                        return hwnd
                        
            except Exception as e:
                print(f"搜索'{keyword}'窗口时出错: {e}")
                continue
        
        print("❌ 未找到相关窗口")
        return None
    
    def analyze_window_controls(self):
        """分析窗口中的控件，寻找可填写的字段"""
        if not self.window:
            print("❌ 窗口未初始化")
            return
        
        print("\n🔍 分析窗口控件...")
        try:
            # 获取所有控件
            controls = self.window.descendants()
            
            # 寻找输入框和相关标签
            input_controls = []
            for ctrl in controls:
                try:
                    control_type = ctrl.element_info.control_type
                    class_name = ctrl.class_name()
                    text = ctrl.window_text()
                    
                    # 查找编辑框、文本框等输入控件
                    if any(keyword in class_name.lower() for keyword in ['edit', 'text', 'input']):
                        input_controls.append({
                            'control': ctrl,
                            'type': control_type,
                            'class': class_name,
                            'text': text,
                            'automation_id': getattr(ctrl.element_info, 'automation_id', '')
                        })
                        print(f"  📝 输入框: {class_name} - '{text}' - ID: {getattr(ctrl.element_info, 'automation_id', '')}")
                
                except Exception as e:
                    continue
            
            return input_controls
            
        except Exception as e:
            print(f"分析控件失败: {e}")
            return []
    
    def smart_fill_form(self):
        """智能填写表单"""
        if not self.user_data.get('name') or not self.user_data.get('id_card'):
            print("❌ 请先在配置文件中填写姓名和身份证信息")
            return False
        
        print(f"\n🚀 开始自动填写...")
        print(f"姓名: {self.user_data['name']}")
        print(f"身份证: {self.user_data['id_card'][:6]}****{self.user_data['id_card'][-4:]}")
        
        input_controls = self.analyze_window_controls()
        if not input_controls:
            print("❌ 未找到可填写的输入框")
            return False
        
        # 智能匹配字段
        filled_count = 0
        for ctrl_info in input_controls:
            try:
                ctrl = ctrl_info['control']
                text = ctrl_info['text'].lower()
                automation_id = ctrl_info['automation_id'].lower()
                
                # 判断字段类型并填写
                if any(keyword in text or keyword in automation_id for keyword in ['姓名', 'name', '名字']):
                    print(f"  ✏️  填写姓名: {ctrl_info['class']}")
                    ctrl.set_text(self.user_data['name'])
                    filled_count += 1
                    time.sleep(0.5)
                    
                elif any(keyword in text or keyword in automation_id for keyword in ['身份证', 'id', 'card', '证件']):
                    print(f"  ✏️  填写身份证: {ctrl_info['class']}")
                    ctrl.set_text(self.user_data['id_card'])
                    filled_count += 1
                    time.sleep(0.5)
                    
                elif any(keyword in text or keyword in automation_id for keyword in ['手机', 'phone', '电话']):
                    if self.user_data.get('phone'):
                        print(f"  ✏️  填写手机号: {ctrl_info['class']}")
                        ctrl.set_text(self.user_data['phone'])
                        filled_count += 1
                        time.sleep(0.5)
                        
                elif any(keyword in text or keyword in automation_id for keyword in ['邮箱', 'email', 'mail']):
                    if self.user_data.get('email'):
                        print(f"  ✏️  填写邮箱: {ctrl_info['class']}")
                        ctrl.set_text(self.user_data['email'])
                        filled_count += 1
                        time.sleep(0.5)
                        
            except Exception as e:
                print(f"填写控件失败: {e}")
                continue
        
        print(f"\n✅ 填写完成！共填写了 {filled_count} 个字段")
        return filled_count > 0
    
    def run(self):
        """运行自动填写流程"""
        print("🎯 天安门广场预约自动填写工具启动")
        print("=" * 50)
        
        # 检查配置
        if not self.user_data.get('name') or not self.user_data.get('id_card'):
            print(f"⚠️  请先在 {self.config_file} 中填写您的个人信息")
            return False
        
        # 查找窗口
        hwnd = self.find_target_window()
        if not hwnd:
            print("❌ 请先打开天安门广场预约页面（浏览器或微信小程序）")
            return False
        
        # 等待用户确认
        print(f"\n已找到目标窗口: {self.window.window_text()}")
        confirm = input("是否继续自动填写？(y/n): ").lower().strip()
        if confirm != 'y':
            print("用户取消操作")
            return False
        
        # 执行填写
        success = self.smart_fill_form()
        
        if success:
            print("\n🎉 自动填写完成！请检查填写结果并手动提交")
        else:
            print("\n❌ 自动填写失败，请手动操作")
        
        return success

def main():
    """主函数"""
    auto_fill = TianamenAutoFill()
    auto_fill.run()

if __name__ == "__main__":
    main()
