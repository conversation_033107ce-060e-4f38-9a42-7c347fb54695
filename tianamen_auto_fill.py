#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
天安门广场预约微信小程序自动填写工具
精简版 - 只支持姓名、身份证号、手机号三个字段
"""

from pywinauto import Application
from pywinauto.findwindows import find_windows
import time

# 用户配置
USER_CONFIG = {
    "name": "李明",           # 请修改为您的真实姓名
    "id_card": "110101199001015678",  # 请修改为您的真实身份证号
    "phone": "13912345678"    # 请修改为您的真实手机号
}

def find_wechat_window():
    """查找微信小程序窗口"""
    keywords = ["微信", "WeChat", "天安门", "预约"]

    for keyword in keywords:
        try:
            windows = find_windows(title_re=f".*{keyword}.*", visible_only=True)
            if windows:
                for hwnd in windows:
                    app = Application(backend="uia").connect(handle=hwnd)
                    window = app.window(handle=hwnd)
                    title = window.window_text()

                    if any(kw in title for kw in ["天安门", "预约", "微信"]):
                        print(f"找到目标窗口: {title}")
                        return app, window
        except:
            continue

    print("未找到微信小程序窗口")
    return None, None

def find_input_controls(window):
    """查找窗口中的输入控件"""
    input_controls = []
    try:
        controls = window.descendants()
        for ctrl in controls:
            try:
                class_name = ctrl.class_name().lower()
                if any(keyword in class_name for keyword in ['edit', 'text', 'input']):
                    input_controls.append(ctrl)
            except:
                continue
    except:
        pass
    return input_controls

def fill_form(window):
    """自动填写表单"""
    print("开始自动填写...")
    print(f"姓名: {USER_CONFIG['name']}")
    print(f"身份证: {USER_CONFIG['id_card'][:6]}****{USER_CONFIG['id_card'][-4:]}")
    print(f"手机号: {USER_CONFIG['phone'][:3]}****{USER_CONFIG['phone'][-4:]}")

    input_controls = find_input_controls(window)
    if not input_controls:
        print("未找到输入框")
        return False

    filled_count = 0

    # 尝试填写每个输入框
    for i, ctrl in enumerate(input_controls):
        try:
            # 点击输入框获取焦点
            ctrl.click_input()
            time.sleep(0.3)

            # 根据输入框位置判断字段类型（简单策略）
            if i == 0:  # 第一个输入框通常是姓名
                ctrl.set_text(USER_CONFIG['name'])
                print(f"填写姓名: {USER_CONFIG['name']}")
                filled_count += 1
            elif i == 1:  # 第二个输入框通常是身份证
                ctrl.set_text(USER_CONFIG['id_card'])
                print(f"填写身份证: {USER_CONFIG['id_card']}")
                filled_count += 1
            elif i == 2:  # 第三个输入框通常是手机号
                ctrl.set_text(USER_CONFIG['phone'])
                print(f"填写手机号: {USER_CONFIG['phone']}")
                filled_count += 1

            time.sleep(0.5)

        except Exception as e:
            print(f"填写第{i+1}个输入框失败: {e}")
            continue

    print(f"填写完成！共填写了 {filled_count} 个字段")
    return filled_count > 0

def main():
    """主函数"""
    print("天安门广场预约自动填写工具")
    print("=" * 30)

    # 检查配置
    if not USER_CONFIG['name'] or not USER_CONFIG['id_card'] or not USER_CONFIG['phone']:
        print("请先修改脚本中的USER_CONFIG配置")
        return

    # 查找微信窗口
    app, window = find_wechat_window()
    if not window:
        print("请先打开微信小程序")
        return

    # 确认填写
    confirm = input("是否开始自动填写？(y/n): ")
    if confirm.lower() != 'y':
        print("用户取消")
        return

    # 执行填写
    success = fill_form(window)
    if success:
        print("自动填写完成！请检查并手动提交")
    else:
        print("自动填写失败，请手动操作")

if __name__ == "__main__":
    main()
