#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
天安门广场预约浏览器自动填写脚本
支持Chrome、Edge等浏览器的自动化填写
"""

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
import json
import time
import os
from typing import Dict, List, Optional

class BrowserAutoFill:
    def __init__(self, config_file: str = "user_config.json"):
        """
        初始化浏览器自动填写工具
        
        Args:
            config_file: 用户配置文件路径
        """
        self.config_file = config_file
        self.user_data = self.load_user_config()
        self.driver = None
        self.wait = None
        
    def load_user_config(self) -> Dict:
        """加载用户配置信息"""
        if os.path.exists(self.config_file):
            try:
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except Exception as e:
                print(f"读取配置文件失败: {e}")
                return {}
        else:
            print(f"配置文件 {self.config_file} 不存在，请先创建")
            return {}
    
    def setup_browser(self, headless: bool = False) -> bool:
        """
        设置浏览器驱动
        
        Args:
            headless: 是否无头模式运行
            
        Returns:
            是否设置成功
        """
        try:
            # Chrome选项
            chrome_options = Options()
            if headless:
                chrome_options.add_argument('--headless')
            chrome_options.add_argument('--no-sandbox')
            chrome_options.add_argument('--disable-dev-shm-usage')
            chrome_options.add_argument('--disable-blink-features=AutomationControlled')
            chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
            chrome_options.add_experimental_option('useAutomationExtension', False)
            
            # 尝试启动Chrome
            try:
                self.driver = webdriver.Chrome(options=chrome_options)
                self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
                self.wait = WebDriverWait(self.driver, 10)
                print("✅ Chrome浏览器启动成功")
                return True
            except Exception as e:
                print(f"Chrome启动失败: {e}")
                
            # 尝试启动Edge
            try:
                from selenium.webdriver.edge.service import Service as EdgeService
                from selenium.webdriver.edge.options import Options as EdgeOptions
                
                edge_options = EdgeOptions()
                if headless:
                    edge_options.add_argument('--headless')
                edge_options.add_argument('--no-sandbox')
                edge_options.add_argument('--disable-dev-shm-usage')
                
                self.driver = webdriver.Edge(options=edge_options)
                self.wait = WebDriverWait(self.driver, 10)
                print("✅ Edge浏览器启动成功")
                return True
            except Exception as e:
                print(f"Edge启动失败: {e}")
                
            print("❌ 无法启动浏览器，请确保已安装Chrome或Edge浏览器")
            return False
            
        except Exception as e:
            print(f"浏览器设置失败: {e}")
            return False
    
    def find_form_fields(self) -> Dict[str, List]:
        """
        查找页面中的表单字段
        
        Returns:
            字段类型到元素列表的映射
        """
        fields = {
            'name': [],
            'id_card': [],
            'phone': [],
            'email': []
        }
        
        try:
            # 查找所有输入框
            inputs = self.driver.find_elements(By.TAG_NAME, "input")
            textareas = self.driver.find_elements(By.TAG_NAME, "textarea")
            all_inputs = inputs + textareas
            
            print(f"🔍 找到 {len(all_inputs)} 个输入字段")
            
            for element in all_inputs:
                try:
                    # 获取元素属性
                    element_id = element.get_attribute('id') or ''
                    element_name = element.get_attribute('name') or ''
                    element_class = element.get_attribute('class') or ''
                    element_placeholder = element.get_attribute('placeholder') or ''
                    element_type = element.get_attribute('type') or ''
                    
                    # 获取相关标签文本
                    label_text = ''
                    try:
                        # 查找关联的label
                        if element_id:
                            label = self.driver.find_element(By.CSS_SELECTOR, f"label[for='{element_id}']")
                            label_text = label.text
                    except:
                        pass
                    
                    # 合并所有文本用于匹配
                    all_text = f"{element_id} {element_name} {element_class} {element_placeholder} {label_text}".lower()
                    
                    print(f"  📝 字段: {element_type} - {all_text[:50]}...")
                    
                    # 智能分类
                    if any(keyword in all_text for keyword in ['姓名', 'name', '名字', '真实姓名']):
                        fields['name'].append(element)
                        print(f"    ✅ 识别为姓名字段")
                        
                    elif any(keyword in all_text for keyword in ['身份证', 'id', 'card', '证件号', 'idcard']):
                        fields['id_card'].append(element)
                        print(f"    ✅ 识别为身份证字段")
                        
                    elif any(keyword in all_text for keyword in ['手机', 'phone', '电话', 'mobile', 'tel']):
                        fields['phone'].append(element)
                        print(f"    ✅ 识别为手机号字段")
                        
                    elif any(keyword in all_text for keyword in ['邮箱', 'email', 'mail', '电子邮件']):
                        fields['email'].append(element)
                        print(f"    ✅ 识别为邮箱字段")
                        
                except Exception as e:
                    continue
            
            return fields
            
        except Exception as e:
            print(f"查找表单字段失败: {e}")
            return fields
    
    def fill_field(self, element, value: str, field_name: str) -> bool:
        """
        填写单个字段
        
        Args:
            element: 页面元素
            value: 要填写的值
            field_name: 字段名称（用于日志）
            
        Returns:
            是否填写成功
        """
        try:
            # 滚动到元素可见
            self.driver.execute_script("arguments[0].scrollIntoView(true);", element)
            time.sleep(0.5)
            
            # 点击元素获取焦点
            element.click()
            time.sleep(0.3)
            
            # 清空现有内容
            element.clear()
            time.sleep(0.3)
            
            # 输入新值
            element.send_keys(value)
            time.sleep(0.5)
            
            # 验证输入
            current_value = element.get_attribute('value')
            if current_value == value:
                print(f"    ✅ {field_name} 填写成功")
                return True
            else:
                print(f"    ⚠️  {field_name} 填写可能不完整: 期望'{value}', 实际'{current_value}'")
                return False
                
        except Exception as e:
            print(f"    ❌ {field_name} 填写失败: {e}")
            return False
    
    def auto_fill_form(self) -> bool:
        """
        自动填写表单
        
        Returns:
            是否填写成功
        """
        if not self.user_data.get('name') or not self.user_data.get('id_card'):
            print("❌ 请先在配置文件中填写姓名和身份证信息")
            return False
        
        print(f"\n🚀 开始自动填写表单...")
        print(f"姓名: {self.user_data['name']}")
        print(f"身份证: {self.user_data['id_card'][:6]}****{self.user_data['id_card'][-4:]}")
        
        # 查找表单字段
        fields = self.find_form_fields()
        filled_count = 0
        
        # 填写姓名
        if fields['name'] and self.user_data.get('name'):
            for element in fields['name']:
                if self.fill_field(element, self.user_data['name'], '姓名'):
                    filled_count += 1
                    break
        
        # 填写身份证
        if fields['id_card'] and self.user_data.get('id_card'):
            for element in fields['id_card']:
                if self.fill_field(element, self.user_data['id_card'], '身份证'):
                    filled_count += 1
                    break
        
        # 填写手机号
        if fields['phone'] and self.user_data.get('phone'):
            for element in fields['phone']:
                if self.fill_field(element, self.user_data['phone'], '手机号'):
                    filled_count += 1
                    break
        
        # 填写邮箱
        if fields['email'] and self.user_data.get('email'):
            for element in fields['email']:
                if self.fill_field(element, self.user_data['email'], '邮箱'):
                    filled_count += 1
                    break
        
        print(f"\n✅ 自动填写完成！共填写了 {filled_count} 个字段")
        return filled_count > 0
    
    def run(self, url: str = None):
        """
        运行自动填写流程
        
        Args:
            url: 目标网页URL，如果为None则使用当前页面
        """
        print("🎯 天安门广场预约浏览器自动填写工具启动")
        print("=" * 50)
        
        # 检查配置
        if not self.user_data.get('name') or not self.user_data.get('id_card'):
            print(f"⚠️  请先在 {self.config_file} 中填写您的个人信息")
            return False
        
        # 设置浏览器
        if not self.setup_browser():
            return False
        
        try:
            # 打开网页
            if url:
                print(f"🌐 正在打开: {url}")
                self.driver.get(url)
                time.sleep(3)
            else:
                print("📄 使用当前页面进行填写")
            
            # 等待用户确认
            current_url = self.driver.current_url
            print(f"\n当前页面: {current_url}")
            confirm = input("是否继续自动填写？(y/n): ").lower().strip()
            if confirm != 'y':
                print("用户取消操作")
                return False
            
            # 执行填写
            success = self.auto_fill_form()
            
            if success:
                print("\n🎉 自动填写完成！请检查填写结果并手动提交")
                input("按回车键关闭浏览器...")
            else:
                print("\n❌ 自动填写失败，请手动操作")
                input("按回车键关闭浏览器...")
            
            return success
            
        except Exception as e:
            print(f"运行过程中出错: {e}")
            return False
        finally:
            if self.driver:
                self.driver.quit()

def main():
    """主函数"""
    auto_fill = BrowserAutoFill()
    
    # 可以指定URL，或者留空使用当前页面
    tianamen_url = None  # 如果知道具体URL可以填写
    auto_fill.run(tianamen_url)

if __name__ == "__main__":
    main()
