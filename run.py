#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
天安门广场预约自动填写工具 - 启动器
提供简单的菜单选择界面
"""

import os
import sys
import json

def check_config():
    """检查配置文件是否存在且已填写"""
    config_file = "user_config.json"
    if not os.path.exists(config_file):
        print("❌ 配置文件不存在")
        return False
    
    try:
        with open(config_file, 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        if not config.get('name') or not config.get('id_card'):
            print("❌ 配置文件中缺少姓名或身份证信息")
            return False
        
        if config.get('name') == "张三" or config.get('id_card') == "110101199001011234":
            print("❌ 请修改配置文件中的示例数据为您的真实信息")
            return False
        
        print("✅ 配置文件检查通过")
        return True
        
    except Exception as e:
        print(f"❌ 配置文件读取失败: {e}")
        return False

def show_menu():
    """显示主菜单"""
    print("\n" + "="*50)
    print("🎯 天安门广场预约自动填写工具")
    print("="*50)
    print("请选择运行模式：")
    print()
    print("1. Windows应用模式 (微信小程序、桌面应用)")
    print("2. 浏览器网页模式 (Chrome、Edge)")
    print("3. 编辑配置文件")
    print("4. 查看使用说明")
    print("0. 退出")
    print()

def edit_config():
    """编辑配置文件"""
    config_file = "user_config.json"
    
    print("\n📝 编辑个人信息配置")
    print("-" * 30)
    
    # 读取现有配置
    config = {}
    if os.path.exists(config_file):
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)
        except:
            pass
    
    # 获取用户输入
    print("请输入您的个人信息（直接回车保持原值）：")
    
    current_name = config.get('name', '')
    name = input(f"姓名 [{current_name}]: ").strip()
    if name:
        config['name'] = name
    elif not current_name:
        config['name'] = ''
    
    current_id = config.get('id_card', '')
    id_card = input(f"身份证号 [{current_id[:6]}****{current_id[-4:] if len(current_id) >= 10 else current_id}]: ").strip()
    if id_card:
        config['id_card'] = id_card
    elif not current_id:
        config['id_card'] = ''
    
    current_phone = config.get('phone', '')
    phone = input(f"手机号 [{current_phone}]: ").strip()
    if phone:
        config['phone'] = phone
    elif not current_phone:
        config['phone'] = ''
    
    current_email = config.get('email', '')
    email = input(f"邮箱 [{current_email}]: ").strip()
    if email:
        config['email'] = email
    elif not current_email:
        config['email'] = ''
    
    # 保存配置
    try:
        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(config, f, ensure_ascii=False, indent=2)
        print("✅ 配置保存成功")
    except Exception as e:
        print(f"❌ 配置保存失败: {e}")

def show_help():
    """显示使用说明"""
    print("\n📖 使用说明")
    print("-" * 30)
    print("1. 首次使用请先编辑配置文件，填写您的个人信息")
    print("2. Windows应用模式：适用于微信小程序等桌面应用")
    print("3. 浏览器网页模式：适用于网页版预约系统")
    print("4. 使用完毕后建议删除配置文件保护隐私")
    print("5. 详细说明请查看 README.md 文件")
    print()
    print("⚠️  注意事项：")
    print("- 仅供个人学习使用，请遵守相关规定")
    print("- 填写前请确认页面已加载完成")
    print("- 如遇问题请手动完成剩余步骤")

def run_windows_mode():
    """运行Windows应用模式"""
    print("\n🚀 启动Windows应用模式...")
    print("请确保已打开天安门预约相关应用（如微信小程序）")
    input("准备好后按回车键继续...")
    
    try:
        import tianamen_auto_fill
        auto_fill = tianamen_auto_fill.TianamenAutoFill()
        auto_fill.run()
    except ImportError:
        print("❌ 缺少依赖库，请运行: pip install pywinauto")
    except Exception as e:
        print(f"❌ 运行失败: {e}")

def run_browser_mode():
    """运行浏览器网页模式"""
    print("\n🚀 启动浏览器网页模式...")
    
    try:
        import browser_auto_fill
        auto_fill = browser_auto_fill.BrowserAutoFill()
        auto_fill.run()
    except ImportError:
        print("❌ 缺少依赖库，请运行: pip install selenium")
    except Exception as e:
        print(f"❌ 运行失败: {e}")

def main():
    """主函数"""
    while True:
        show_menu()
        
        try:
            choice = input("请选择 (0-4): ").strip()
            
            if choice == '0':
                print("👋 再见！")
                break
            elif choice == '1':
                if check_config():
                    run_windows_mode()
                else:
                    print("请先编辑配置文件")
            elif choice == '2':
                if check_config():
                    run_browser_mode()
                else:
                    print("请先编辑配置文件")
            elif choice == '3':
                edit_config()
            elif choice == '4':
                show_help()
            else:
                print("❌ 无效选择，请重新输入")
                
        except KeyboardInterrupt:
            print("\n\n👋 用户中断，再见！")
            break
        except Exception as e:
            print(f"❌ 发生错误: {e}")
        
        input("\n按回车键继续...")

if __name__ == "__main__":
    main()
