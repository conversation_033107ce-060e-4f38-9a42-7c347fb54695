#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CSV文件分割工具 v2
将ct.csv文件分成3个CSV文件，数据分布更均匀
"""

import pandas as pd
import os
import sys

def split_csv_to_excel_sheets():
    """
    将制表符分隔的CSV文件分割为Excel文件的多个sheet
    """
    input_file = 'ct2.csv'  # 新的制表符分隔文件
    output_file = 'ct2_分表.xlsx'
    num_sheets = 3  # 分成3个sheet
    
    print(f"开始处理文件: {input_file}")
    print(f"输出文件: {output_file}")
    print(f"将分割为 {num_sheets} 个Excel sheet")

    # 检查文件是否存在
    if not os.path.exists(input_file):
        print(f"错误：文件 {input_file} 不存在！")
        return False

    try:
        # 先读取文件头部，了解数据结构
        print("正在读取文件头部信息...")
        # 使用制表符分隔符读取文件
        sample_df = pd.read_csv(input_file, sep='\t', nrows=5, on_bad_lines='skip', encoding='utf-8')
        print(f"文件列数: {len(sample_df.columns)}")
        print(f"列名: {list(sample_df.columns)}")
        
        # 获取文件总行数（不包括表头）
        print("正在计算文件总行数...")
        total_rows = sum(1 for line in open(input_file, 'r', encoding='utf-8')) - 1  # 减去表头
        print(f"文件总行数（不含表头）: {total_rows:,}")

        # 计算每个sheet的行数（尽量平均分配）
        rows_per_sheet = total_rows // num_sheets
        remainder = total_rows % num_sheets
        print(f"每个sheet约 {rows_per_sheet:,} 行")

        # 创建Excel写入器
        with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
            # 分批读取并保存到不同的sheet
            for i in range(num_sheets):
                # 计算当前sheet的行数（前面的sheet多分配余数）
                current_rows = rows_per_sheet + (1 if i < remainder else 0)
                start_row = sum(rows_per_sheet + (1 if j < remainder else 0) for j in range(i))
                end_row = start_row + current_rows

                print(f"\n正在处理第 {i+1} 个sheet...")
                print(f"行范围: {start_row+1} 到 {end_row}")
                print(f"预计行数: {current_rows:,}")

                # 读取指定行数的数据，使用制表符分隔符
                if i == 0:
                    # 第一个sheet，包含表头
                    chunk_df = pd.read_csv(input_file, sep='\t', nrows=current_rows, on_bad_lines='skip', encoding='utf-8')
                else:
                    # 后续sheet，跳过表头和前面的数据行
                    chunk_df = pd.read_csv(input_file, sep='\t', skiprows=start_row + 1, nrows=current_rows, on_bad_lines='skip', encoding='utf-8')

                # 生成sheet名称
                sheet_name = f'数据表_{i+1}'

                # 保存到Excel的sheet中
                chunk_df.to_excel(writer, sheet_name=sheet_name, index=False)
                print(f"已保存到sheet: {sheet_name} ({len(chunk_df):,} 行)")

        print(f"\n✅ 分割完成！已生成Excel文件: {output_file}")
        print(f"包含 {num_sheets} 个sheet，数据分布均匀")
        return True
        
    except Exception as e:
        print(f"❌ 处理过程中出现错误: {str(e)}")
        return False

if __name__ == "__main__":
    print("制表符分隔文件转Excel分表工具启动...")
    success = split_csv_to_excel_sheets()
    if success:
        print("任务完成！")
    else:
        print("任务失败！")
