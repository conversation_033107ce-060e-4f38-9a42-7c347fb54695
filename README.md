# 天安门广场预约自动填写工具

这是一个帮助自动填写天安门广场预约表单的Python工具集，支持两种模式：

## 🚀 功能特性

- ✅ 自动识别表单字段（姓名、身份证、手机号、邮箱）
- ✅ 智能匹配输入框
- ✅ 安全的配置文件管理
- ✅ 支持Windows应用程序和浏览器两种模式
- ✅ 详细的操作日志和错误提示

## 📁 文件说明

| 文件名 | 说明 |
|--------|------|
| `tianamen_auto_fill.py` | Windows应用程序自动填写（支持微信小程序等） |
| `browser_auto_fill.py` | 浏览器网页自动填写（支持Chrome、Edge） |
| `user_config.json` | 用户个人信息配置文件 |
| `README.md` | 使用说明文档 |

## 🛠️ 环境准备

### 1. 安装Python依赖

```bash
pip install pywinauto selenium
```

### 2. 浏览器驱动（仅浏览器模式需要）

- **Chrome**: 会自动下载对应版本的ChromeDriver
- **Edge**: 会自动使用系统内置的EdgeDriver

## ⚙️ 配置设置

### 1. 编辑配置文件

打开 `user_config.json` 文件，填写您的真实信息：

```json
{
  "name": "您的真实姓名",
  "id_card": "您的身份证号码",
  "phone": "您的手机号码",
  "email": "您的邮箱地址"
}
```

**⚠️ 安全提示**: 使用完毕后建议删除配置文件，避免个人信息泄露。

## 🎯 使用方法

### 方式一：Windows应用程序模式

适用于微信小程序、桌面应用等：

```bash
python tianamen_auto_fill.py
```

**使用步骤**：
1. 先打开天安门预约相关的应用（如微信小程序）
2. 运行脚本
3. 脚本会自动检测窗口并提示确认
4. 确认后自动填写表单

### 方式二：浏览器网页模式

适用于网页版预约系统：

```bash
python browser_auto_fill.py
```

**使用步骤**：
1. 运行脚本（会自动打开浏览器）
2. 手动导航到预约页面，或修改脚本中的URL
3. 确认后自动填写表单

## 🔧 高级配置

### 自定义目标URL（浏览器模式）

编辑 `browser_auto_fill.py` 文件，修改 `main()` 函数中的URL：

```python
def main():
    auto_fill = BrowserAutoFill()
    tianamen_url = "https://your-reservation-url.com"  # 填写具体URL
    auto_fill.run(tianamen_url)
```

### 无头模式运行（浏览器模式）

如果不想显示浏览器窗口，可以启用无头模式：

```python
# 在 setup_browser() 调用时传入 headless=True
self.setup_browser(headless=True)
```

## 🛡️ 安全说明

1. **个人信息保护**: 配置文件包含敏感信息，请妥善保管
2. **使用后清理**: 建议使用完毕后删除配置文件
3. **仅限个人使用**: 请勿用于商业用途或批量操作
4. **遵守规定**: 请遵守相关网站的使用条款

## 🐛 常见问题

### Q: 脚本找不到窗口怎么办？
A: 确保已打开相关应用，并且窗口标题包含"天安门"、"预约"等关键词。

### Q: 浏览器启动失败怎么办？
A: 确保已安装Chrome或Edge浏览器，并且版本较新。

### Q: 字段识别不准确怎么办？
A: 可以查看脚本输出的字段分析信息，手动调整识别逻辑。

### Q: 填写失败怎么办？
A: 检查网页是否有验证码、动态加载等特殊情况，可能需要手动处理。

## 📝 更新日志

- **v1.0**: 初始版本，支持基本的自动填写功能
- 支持Windows应用程序和浏览器两种模式
- 智能字段识别和安全配置管理

## 🤝 贡献

欢迎提交Issue和Pull Request来改进这个工具！

## ⚖️ 免责声明

本工具仅供学习和个人使用，使用者需自行承担使用风险，开发者不承担任何责任。请遵守相关法律法规和网站使用条款。
