# 天安门广场预约自动填写工具（精简版）

微信小程序专用的自动填写工具，只支持姓名、身份证号、手机号三个核心字段。

## 🚀 功能特性

- ✅ 自动检测微信小程序窗口
- ✅ 自动填写三个核心字段：姓名、身份证号、手机号
- ✅ 代码精简，单文件解决方案
- ✅ 无需配置文件，直接修改代码中的配置

## 🛠️ 环境准备

安装Python依赖：

```bash
pip install pywinauto
```

## ⚙️ 配置设置

编辑 `tianamen_auto_fill.py` 文件中的 `USER_CONFIG` 部分：

```python
USER_CONFIG = {
    "name": "您的真实姓名",           # 请修改为您的真实姓名
    "id_card": "您的身份证号码",      # 请修改为您的真实身份证号
    "phone": "您的手机号码"          # 请修改为您的真实手机号
}
```

## 🎯 使用方法

1. **打开微信小程序**：先在微信中打开天安门广场预约小程序
2. **运行脚本**：
   ```bash
   python tianamen_auto_fill.py
   ```
3. **确认填写**：脚本会自动检测窗口，确认后开始填写
4. **检查结果**：填写完成后请检查并手动提交

## 🔧 工作原理

- 自动搜索包含"微信"、"天安门"、"预约"关键词的窗口
- 查找窗口中的输入控件
- 按顺序填写：第1个输入框=姓名，第2个=身份证，第3个=手机号
- 简单有效的填写策略

## 🛡️ 安全说明

- 个人信息直接写在代码中，使用后请及时删除或修改
- 仅限个人使用，请遵守相关规定
- 填写完成后请手动检查信息准确性

## 🐛 常见问题

**Q: 找不到窗口？**
A: 确保微信小程序已打开，窗口标题包含相关关键词

**Q: 填写顺序不对？**
A: 可以修改代码中的填写逻辑，调整字段顺序

**Q: 某个字段填写失败？**
A: 检查输入框是否可编辑，可能需要手动处理特殊情况
